import { MCPClient } from "@mastra/mcp";

// 构建服务器配置，只包含可用的服务
const servers: any = {};

// 添加 Zapier MCP 服务器（如果 URL 可用）
if (process.env.ZAPIER_MCP_URL) {
  servers.zapier = {
    url: new URL(process.env.ZAPIER_MCP_URL),
  };
}

// 添加 Hacker News MCP 服务器
servers.hackernews = {
  command: "npx",
  args: ["-y", "@devabdultech/hn-mcp-server"],
};

// MySQL MCP 服务器配置
if (process.env.MYSQL_HOST && process.env.MYSQL_USER && process.env.MYSQL_PASS) {
  servers.mysql = {
    command: "npx",
    args: ["-y", "@benborla29/mcp-server-mysql"], 
    env: {
      MYSQL_HOST: process.env.MYSQL_HOST,
      MYSQL_PORT: process.env.MYSQL_PORT || "3306",
      MYSQL_USER: process.env.MYSQL_USER,
      MYSQL_PASS: process.env.MYSQL_PASS, 
      MYSQL_DB: process.env.MYSQL_DB || "hotel", 
    },
  };
  console.log("🔧 MySQL MCP 服务器已配置");
} else {
  console.log("⚠️  MySQL MCP 服务器未配置 - 缺少环境变量");
}

const mcp = new MCPClient({
  servers,
});

// 使用 try-catch 处理工具获取错误
let mcpTools: any = {};
try {
  mcpTools = await mcp.getTools();
  console.log("✅ MCP 工具加载成功");
} catch (error) {
  console.error("❌ MCP 工具加载失败:", error);
  // 如果 MCP 工具加载失败，返回空对象以避免应用崩溃
  mcpTools = {};
}

export { mcpTools };
