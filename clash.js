// ---------------------------------
// Clash Verge 自定义脚本 V1.0 _LINUX DO feat xiaomu
// ---------------------------------

// --- 用户参数设置 ---
const MULTIPLIER_FILTER = 1;  // 0=不过滤，1=过滤大于1x的节点
const CLEAN_KEYWORDS = ["注册", "试用", "官网", "邀请", "下载", "测速", "购买", "代理", "广告", "公众号", "订阅"];

// --- 自定义域名规则配置 ---
// --- 规则: "规则类型,域名/关键词,策略组名称" --- 
//   - DOMAIN-SUFFIX: 域名后缀匹配 ("DOMAIN-SUFFIX,google.com,🚀 代理策略" 会匹配 www.google.com, ads.google.com)
//   - DOMAIN: 域名完全匹配 ("DOMAIN,www.google.com,🚀 代理策略" 只匹配 www.google.com)
//   - DOMAIN-KEYWORD: 域名关键词匹配 ("DOMAIN-KEYWORD,google,🚀 代理策略" 会匹配包含google的任何域名)

const forceProxyDomains = [
    "DOMAIN-SUFFIX,ip138.com,🚀 代理策略",
    "DOMAIN-SUFFIX,localhost,✅ 国内直连",
    "DOMAIN,gemini.google.com,🤖 AI 线路",
    "DOMAIN-SUFFIX,genspark.ai,🤖 AI 线路",
    "DOMAIN-SUFFIX,codeium.com,🤖 AI 线路",
    "DOMAIN-SUFFIX,google.com,✨ 自动优选",
    "DOMAIN-SUFFIX,googleapis.cn,🚀 代理策略",
    "DOMAIN-SUFFIX,googleapis.com,🚀 代理策略",
    "DOMAIN-SUFFIX,gstatic.com,🚀 代理策略",
    "DOMAIN-SUFFIX,xn--ngstr-lra8j.com,🚀 代理策略",
    "DOMAIN-SUFFIX,github.io,🚀 代理策略",
    "DOMAIN-SUFFIX,clarity.ms,🚀 代理策略",
    "DOMAIN-SUFFIX,linux.do,🚀 代理策略",
    "IP-CIDR,***************/32,公司",
    "IP-CIDR,************/32,公司",
];

const ruleConfig = [
    {
        name: "广告规则",
        group: "🛑 广告拦截",
        url: "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/AdvertisingLite/AdvertisingLite.yaml"
    }
];
// --- 修正后的函数，请用它替换你脚本中旧的 cleanProxies 函数 ---
function cleanProxies(proxies) {
    // 过滤有害关键词的逻辑保持不变
    const cleanedByKeyword = proxies.filter(p => {
        const name = typeof p.name === 'string' ? p.name : '';
        if (!name) return false;
        if (CLEAN_KEYWORDS.some(k => name.toLowerCase().includes(k))) {
            return false;
        }
        return true;
    });

    // 如果 MULTIPLIER_FILTER 设置为 0 或更小，则不过滤倍率，直接返回结果
    if (MULTIPLIER_FILTER <= 0) {
        return cleanedByKeyword;
    }

    // 新的倍率过滤逻辑
    return cleanedByKeyword.filter(p => {
        const name = p.name;
        // 正则表达式，用于从节点名称中提取倍率数字，例如从 "【1.5x】" 中提取 "1.5"
        const multiplierMatch = name.match(/\[?(\d+(?:\.\d+)?)\]?\s*x/i);

        // 如果找到了倍率
        if (multiplierMatch && multiplierMatch[1]) {
            const multiplierValue = parseFloat(multiplierMatch[1]);
            // 只有当节点的实际倍率 > 你设置的阈值时，才过滤掉它 (return false)
            // 否则保留该节点 (return true)
            return multiplierValue <= MULTIPLIER_FILTER;
        }

        // 如果节点名称中没有倍率信息，则默认保留
        return true;
    });
}

function main(config) {
    const newConfig = JSON.parse(JSON.stringify(config));
    newConfig.proxies = cleanProxies(newConfig.proxies);

    const allProxyNames = newConfig.proxies.map(p => p.name);
    const safeAllProxyNames = allProxyNames.length > 0 ? allProxyNames : ['DIRECT'];

    // --- 修改部分：更新正则表达式以包含 Emoji 和更精确的匹配 ---
    const usRegex = /(🇺🇸|US|USA|America|United States|美国|美区|洛杉矶|圣何塞|硅谷|达拉斯|西雅图|芝加哥|纽约)/i;
    const asiaRegex = /(🇭🇰|🇲🇴|🇹🇼|🇯🇵|🇰🇷|🇸🇬|🇻🇳|🇹🇭|🇲🇾|🇵🇭|🇮🇩|🇮🇳|HK|SG|JP|KR|TW|VN|TH|MY|PH|ID|IN|香港|澳门|台湾|日本|东京|大阪|韩国|首尔|新加坡|越南|泰国|马来西亚|菲律宾|印尼|印度)/i;
    const europeRegex = /(🇬🇧|🇩🇪|🇫🇷|🇳🇱|🇮🇪|🇮🇹|🇪🇸|🇨🇭|🇸🇪|🇳🇴|🇫🇮|UK|DE|FR|NL|GB|IE|IT|ES|SE|NO|FI|欧洲|德国|德国|法国|英国|荷兰|爱尔兰|意大利|西班牙|瑞士|瑞典|挪威|芬兰)/i;
    // --- 修改结束 ---

    const usNodeNames = allProxyNames.filter(name => usRegex.test(name));
    const asiaNodeNames = allProxyNames.filter(name => asiaRegex.test(name));
    const europeNodeNames = allProxyNames.filter(name => europeRegex.test(name));

    const getRegionProxies = (regionalNodes) => regionalNodes.length > 0 ? regionalNodes : safeAllProxyNames;

    const proxyGroups = [
        { name: "🚀 代理策略", type: "select", proxies: ["✨ 自动优选", "👆 手动精选", "🌎 美国节点", "🌏 亚洲节点", "🌍 欧洲节点", "✅ 国内直连"] },
        { name: "✨ 自动优选", type: "url-test", url: "http://www.gstatic.com/generate_204", interval: 300, tolerance: 50, proxies: safeAllProxyNames, hidden: true },
        { name: "👆 手动精选", type: "select", proxies: ["✨ 自动优选", "DIRECT", ...safeAllProxyNames] },
        { name: "🌎 美国节点", type: "url-test", url: "http://www.gstatic.com/generate_204", interval: 300, tolerance: 50, proxies: getRegionProxies(usNodeNames), hidden: true },
        { name: "🌏 亚洲节点", type: "url-test", url: "http://www.gstatic.com/generate_204", interval: 300, tolerance: 50, proxies: getRegionProxies(asiaNodeNames), hidden: true },
        { name: "🌍 欧洲节点", type: "url-test", url: "http://www.gstatic.com/generate_204", interval: 300, tolerance: 50, proxies: getRegionProxies(europeNodeNames), hidden: true },
        { name: "🤖 AI 线路", type: "select", proxies: ["🌎 美国节点", "👆 手动精选"] },
        { name: "🛑 广告拦截", type: "select", proxies: ["✅ 显示广告", "🚫 拦截广告"] },
        { name: "🐟 漏网之鱼", type: "select", proxies: ["🚀 代理策略"], hidden: true },
        { name: "✅ 国内直连", type: "select", proxies: ["DIRECT"], hidden: true },
        { name: "🚫 拦截广告", type: "select", proxies: ["REJECT"], hidden: true },
        { name: "✅ 显示广告", type: "select", proxies: ["DIRECT"], hidden: true },
        { name: "公司", type: "select", proxies: ["DIRECT"], hidden: false }
    ];
    const nodeList =
        [
            { "name": "自建-公司-国内-阿里云", "type": "ss", "server": "***************", "port": 38320, "cipher": "2022-blake3-aes-128-gcm", "password": "d29zbWo4Y2k5MWl3dWUydw==" },
        ]
    nodeList.forEach(node => {
        newConfig.proxies.push(node);
        const group = proxyGroups.filter((item) => item.name === "公司");
        if (group) {
            group.forEach(pgn => pgn.proxies.push(node.name));
        }
    });
    const rules = [...forceProxyDomains];
    const ruleProviders = Object.fromEntries(ruleConfig.map(({ name, url, group }) => {
        const key = name.replace(/[^a-zA-Z0-9]/g, '').toLowerCase() + '_rules';
        rules.push(`RULE-SET,${key},${group}`);
        return [key, {
            type: "http",
            behavior: "classical",
            url,
            interval: 86400,
            path: `./rule-providers/${key}.yaml`
        }];
    }));

    rules.push(
        "GEOSITE,openai,🤖 AI 线路",
        "GEOSITE,bing,🤖 AI 线路",
        "GEOSITE,google,✨ 自动优选",
        "GEOSITE,googlefcm,✨ 自动优选",
        "GEOSITE,steam@cn,✅ 国内直连",
        "GEOSITE,category-games@cn,✅ 国内直连",
        "GEOSITE,gfw,🚀 代理策略",
        "GEOSITE,telegram,🚀 代理策略",
        "GEOSITE,onedrive,✅ 国内直连",
        "GEOSITE,microsoft@cn,✅ 国内直连",
        "GEOSITE,private,✅ 国内直连",
        "GEOSITE,cn,✅ 国内直连",
        "GEOIP,telegram,🚀 代理策略",
        "GEOIP,private,✅ 国内直连,no-resolve",
        "GEOIP,cn,✅ 国内直连",
        "MATCH,🐟 漏网之鱼"
    );
    return {
        ...newConfig,
        'proxy-groups': proxyGroups,
        rules,
        'rule-providers': ruleProviders
    };
}