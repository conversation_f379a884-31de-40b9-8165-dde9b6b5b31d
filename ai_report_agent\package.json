{"name": "ai_report_agent", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "<PERSON>ra dev", "build": "mastra build", "start": "mastra start"}, "keywords": [], "author": "", "license": "ISC", "description": "", "type": "module", "engines": {"node": ">=20.9.0"}, "dependencies": {"@ai-sdk/openai": "^1.3.23", "@ai-sdk/openai-compatible": "^0.2.16", "@benborla29/mcp-server-mysql": "^2.0.5", "@mastra/core": "^0.12.0", "@mastra/libsql": "^0.12.0", "@mastra/loggers": "^0.10.5", "@mastra/mcp": "^0.10.8", "@mastra/memory": "^0.12.0", "zod": "^3.25.76"}, "devDependencies": {"@types/node": "^24.1.0", "mastra": "^0.10.16", "typescript": "^5.8.3"}}