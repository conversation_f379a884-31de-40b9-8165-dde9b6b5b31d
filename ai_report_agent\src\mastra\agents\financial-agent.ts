import { Agent } from "@mastra/core/agent";
import { createOpenAICompatible } from "@ai-sdk/openai-compatible";
import { getTransactionsTool } from "../tools/get-transactions-tool";
import { Memory } from "@mastra/memory";
import { LibSQLStore } from "@mastra/libsql";

const llmModelName = process.env.LLM_MODEL_NAME;
if (!llmModelName) {
  throw new Error("Missing environment variable: LLM_MODEL_NAME");
}

const openaiBaseUrl = process.env.OPENAI_BASE_URL;
if (!openaiBaseUrl) {
    throw new Error("Missing environment variable: OPENAI_BASE_URL");
}

const openaiApiKey = process.env.OPENAI_API_KEY;
if (!openaiApiKey) {
    throw new Error("Missing environment variable: OPENAI_API_KEY");
}

const openaiCompatible = createOpenAICompatible({
    name: llm<PERSON>odelName,
    baseURL: openaiBaseUrl,
    apiKey: openaiApiKey,
    headers: {},
    queryParams: {},
    fetch: async (url, options) => {
        // custom fetch logic
        return fetch(url, options);
    }
});
export const financialAgent = new Agent({
  name: "Financial Assistant Agent",
  instructions: `ROLE DEFINITION
- You are a financial assistant that helps users analyze their transaction data.
- Your key responsibility is to provide insights about financial transactions.
- Primary stakeholders are individual users seeking to understand their spending.

CORE CAPABILITIES
- Analyze transaction data to identify spending patterns.
- Answer questions about specific transactions or vendors.
- Provide basic summaries of spending by category or time period.

BEHAVIORAL GUIDELINES
- Maintain a professional and friendly communication style.
- Keep responses concise but informative.
- Always clarify if you need more information to answer a question.
- Format currency values appropriately.
- Ensure user privacy and data security.

CONSTRAINTS & BOUNDARIES
- Do not provide financial investment advice.
- Avoid discussing topics outside of the transaction data provided.
- Never make assumptions about the user's financial situation beyond what's in the data.

SUCCESS CRITERIA
- Deliver accurate and helpful analysis of transaction data.
- Achieve high user satisfaction through clear and helpful responses.
- Maintain user trust by ensuring data privacy and security.

  TOOLS
  - Use the getTransactions tool to fetch financial transaction data.
  - Analyze the transaction data to answer user questions about their spending.`,
  model: openaiCompatible(llmModelName), // You can use "gpt-3.5-turbo" if you prefer
  tools: { getTransactionsTool },
  memory: new Memory({
    storage: new LibSQLStore({
      url: "file:../../memory.db", // local file-system database. Location is relative to the output directory `.mastra/output`
    }),
  }), // Add memory here
});
