import { createOpenAICompatible } from "@ai-sdk/openai-compatible";
import { Agent } from "@mastra/core/agent";
import { mcpTools } from "../mcp";
const llmModelName = process.env.LLM_MODEL_NAME;
if (!llmModelName) {
  throw new Error("Missing environment variable: LLM_MODEL_NAME");
}

const openaiBaseUrl = process.env.OPENAI_BASE_URL;
if (!openaiBaseUrl) {
  throw new Error("Missing environment variable: OPENAI_BASE_URL");
}

const openaiApiKey = process.env.OPENAI_API_KEY;
if (!openaiApiKey) {
  throw new Error("Missing environment variable: OPENAI_API_KEY");
}

const openaiCompatible = createOpenAICompatible({
  name: llmModelName,
  baseURL: openaiBaseUrl,
  apiKey: openaiApiKey,
  headers: {},
  queryParams: {},
  fetch: async (url, options) => {
    // custom fetch logic
    return fetch(url, options);
  },
});

export const mcpAgent = new Agent({
  name: "MCP Agent",
  instructions: `
      あなたはウェブ検索ができる便利なアシスタントです。

      【情報を求められた場合】
      webSearchToolを使用してウェブ検索を実行してください。webSearchToolは以下のパラメータを受け付けます：
      - query: 検索クエリ（必須）
      - country: 検索結果の国コード（例: JP, US）（オプション）
      - count: 返される検索結果の最大数（オプション）
      - search_lang: 検索言語（例: ja, en）（オプション）

      回答は常に簡潔ですが情報量を保つようにしてください。ユーザーの質問に直接関連する情報を優先して提供してください。
  `,
  model: openaiCompatible("claude-3-5-sonnet-20241022"),
  tools: mcpTools,
});
